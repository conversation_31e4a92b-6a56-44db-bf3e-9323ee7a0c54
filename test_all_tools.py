#!/usr/bin/env python3
"""
Comprehensive test for all agent tools
"""

import os
import sys
from agent import AdvancedCodingAgent

def test_all_tools():
    """Test all available tools"""
    print("🧪 Testing All Agent Tools...")
    
    try:
        agent = AdvancedCodingAgent()
        print("✅ Agent initialized successfully")
    except Exception as e:
        print(f"❌ Agent initialization failed: {e}")
        return False
    
    # Get all tools
    tools = agent.create_tools()
    print(f"📊 Found {len(tools)} tools")
    
    # Test a few key tools
    test_cases = [
        ("create_file", "test_file.txt|Hello World"),
        ("file_exists", "test_file.txt"),
        ("append_text_to_file", "test_file.txt|\\nAppended text"),
        ("read_file", "test_file.txt"),
        ("backup_file", "test_file.txt"),
        ("delete_file", "test_file.txt"),
        ("create_directory", "test_dir"),
        ("directory_exists", "test_dir"),
        ("delete_directory", "test_dir|true"),
        ("semantic_web_search", "python programming"),
        ("search_stackoverflow", "python list comprehension"),
        ("github_code_search", "python flask example"),
        ("get_project_setup_info", ""),
        ("natural_language_to_code", "create a hello world function"),
        ("intent_recognition", "I want to create a web scraper"),
        ("chain_of_thought_reasoning", "How to build a REST API"),
    ]
    
    results = []
    for tool_name, test_input in test_cases:
        try:
            # Find the tool
            tool = next((t for t in tools if t.name == tool_name), None)
            if tool:
                print(f"🔧 Testing {tool_name}...")
                result = tool.func(test_input) if test_input else tool.func("")
                if "❌" not in result:
                    results.append(f"✅ {tool_name}")
                else:
                    results.append(f"⚠️ {tool_name} (with warnings)")
            else:
                results.append(f"❌ {tool_name} (not found)")
        except Exception as e:
            results.append(f"❌ {tool_name} (error: {str(e)[:50]})")
    
    print(f"\n📊 Test Results:")
    for result in results:
        print(f"  {result}")
    
    success_count = len([r for r in results if r.startswith("✅")])
    total_count = len(results)
    
    print(f"\n🎯 Summary: {success_count}/{total_count} tools working properly")
    
    # Clean up test files
    try:
        if os.path.exists("test_file.txt"):
            os.remove("test_file.txt")
        if os.path.exists("test_file.txt.bak"):
            os.remove("test_file.txt.bak")
        if os.path.exists("test_dir"):
            os.rmdir("test_dir")
    except:
        pass
    
    return success_count >= total_count * 0.8  # 80% success rate

if __name__ == "__main__":
    success = test_all_tools()
    print(f"\n{'🎉 All tests completed successfully!' if success else '⚠️ Some tests failed, but core functionality works'}")
    sys.exit(0 if success else 1)
