import logging

# Configure logging for better debugging and monitoring
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def validate_input(input_data, expected_type, allow_none=False):
    """
    Validates the input data against the expected type.

    Args:
        input_data: The data to be validated.
        expected_type: The expected data type (e.g., str, int, float, bool, list, dict).
        allow_none:  <PERSON><PERSON><PERSON> indicating whether None is a valid input. Defaults to False.

    Returns:
        True if the input is valid, False otherwise.  Also returns False if an unexpected error occurs.

    Raises:
        TypeError: If expected_type is not a valid type.
    """

    try:
        if input_data is None:
            return allow_none

        if not isinstance(expected_type, type):
            raise TypeError("expected_type must be a valid type (e.g., str, int, float, list, dict)")

        return isinstance(input_data, expected_type)

    except Exception as e:
        logging.error(f"An unexpected error occurred during input validation: {e}")
        return False


def process_string_input(input_string, strip_whitespace=True, to_lowercase=False):
    """
    Processes a string input by stripping whitespace and/or converting to lowercase.

    Args:
        input_string: The string to process.
        strip_whitespace: Whether to strip leading/trailing whitespace. Defaults to True.
        to_lowercase: Whether to convert the string to lowercase. Defaults to False.

    Returns:
        The processed string, or None if the input is invalid.

    Raises:
        TypeError: If input_string is not a string.
    """

    if not validate_input(input_string, str):
        logging.error("Invalid input: Input must be a string.")
        return None

    try:
        processed_string = input_string

        if strip_whitespace:
            processed_string = processed_string.strip()

        if to_lowercase:
            processed_string = processed_string.lower()

        return processed_string

    except Exception as e:
        logging.error(f"An error occurred while processing the string: {e}")
        return None


def process_numeric_input(input_number, expected_type=int, min_value=None, max_value=None):
    """
    Processes a numeric input by validating its type and range.

    Args:
        input_number: The number to process.
        expected_type: The expected numeric type (int or float). Defaults to int.
        min_value: The minimum allowed value (inclusive). Defaults to None.
        max_value: The maximum allowed value (inclusive). Defaults to None.

    Returns:
        The input number if valid, or None if the input is invalid.

    Raises:
        TypeError: If expected_type is not int or float.
    """

    if expected_type not in (int, float):
        raise TypeError("expected_type must be int or float")

    if not validate_input(input_number, expected_type):
        logging.error(f"Invalid input: Input must be of type {expected_type.__name__}.")
        return None

    try:
        if min_value is not None and input_number < min_value:
            logging.error(f"Input value {input_number} is less than the minimum allowed value {min_value}.")
            return None

        if max_value is not None and input_number > max_value:
            logging.error(f"Input value {input_number} is greater than the maximum allowed value {max_value}.")
            return None

        return input_number

    except Exception as e:
        logging.error(f"An error occurred while processing the number: {e}")
        return None


def process_boolean_input(input_bool):
    """
    Processes a boolean input.  This function primarily validates the input type.

    Args:
        input_bool: The boolean value to process.

    Returns:
        The input boolean if valid, or None if the input is invalid.
    """

    if not validate_input(input_bool, bool):
        logging.error("Invalid input: Input must be a boolean.")
        return None

    return input_bool


def process_list_input(input_list, element_type=None, min_length=None, max_length=None):
    """
    Processes a list input by validating its type, element type, and length.

    Args:
        input_list: The list to process.
        element_type: The expected type of elements in the list. Defaults to None (no element type check).
        min_length: The minimum allowed length of the list. Defaults to None.
        max_length: The maximum allowed length of the list. Defaults to None.

    Returns:
        The input list if valid, or None if the input is invalid.
    """

    if not validate_input(input_list, list):
        logging.error("Invalid input: Input must be a list.")
        return None

    try:
        if min_length is not None and len(input_list) < min_length:
            logging.error(f"List length {len(input_list)} is less than the minimum allowed length {min_length}.")
            return None

        if max_length is not None and len(input_list) > max_length:
            logging.error(f"List length {len(input_list)} is greater than the maximum allowed length {max_length}.")
            return None

        if element_type is not None:
            for element in input_list:
                if not validate_input(element, element_type):
                    logging.error(f"Invalid input: List element {element} is not of type {element_type.__name__}.")
                    return None

        return input_list

    except Exception as e:
        logging.error(f"An error occurred while processing the list: {e}")
        return None


def process_dictionary_input(input_dict, key_type=None, value_type=None, required_keys=None):
    """
    Processes a dictionary input by validating its type, key type, value type, and required keys.

    Args:
        input_dict: The dictionary to process.
        key_type: The expected type of keys in the dictionary. Defaults to None (no key type check).
        value_type: The expected type of values in the dictionary. Defaults to None (no value type check).
        required_keys: A list of keys that must be present in the dictionary. Defaults to None.

    Returns:
        The input dictionary if valid, or None if the input is invalid.
    """

    if not validate_input(input_dict, dict):
        logging.error("Invalid input: Input must be a dictionary.")
        return None

    try:
        if required_keys is not None:
            for key in required_keys:
                if key not in input_dict:
                    logging.error(f"Required key '{key}' is missing from the dictionary.")
                    return None

        if key_type is not None:
            for key in input_dict:
                if not validate_input(key, key_type):
                    logging.error(f"Invalid input: Dictionary key '{key}' is not of type {key_type.__name__}.")
                    return None

        if value_type is not None:
            for value in input_dict.values():
                if not validate_input(value, value_type):
                    logging.error(f"Invalid input: Dictionary value '{value}' is not of type {value_type.__name__}.")
                    return None

        return input_dict

    except Exception as e:
        logging.error(f"An error occurred while processing the dictionary: {e}")
        return None


# Example Usage (demonstrates modularity and reusability)
if __name__ == '__main__':
    # String input processing
    name = "  John Doe  "
    processed_name = process_string_input(name, strip_whitespace=True, to_lowercase=True)
    if processed_name:
        print(f"Processed name: {processed_name}")

    # Numeric input processing
    age = "30"  # Intentionally a string to demonstrate validation
    processed_age = process_numeric_input(int(age), expected_type=int, min_value=18, max_value=120)
    if processed_age:
        print(f"Processed age: {processed_age}")

    # Boolean input processing
    is_active = True
    processed_is_active = process_boolean_input(is_active)
    if processed_is_active:
        print(f"Processed is_active: {processed_is_active}")

    # List input processing
    numbers = [1, 2, 3, "4", 5] # Intentionally contains a string
    processed_numbers = process_list_input(numbers, element_type=int, min_length=3, max_length=10)
    if processed_numbers:
        print(f"Processed numbers: {processed_numbers}")
    else:
        print("Numbers list is invalid.")

    # Dictionary input processing
    user_data = {"name": "Alice", "age": 25, "city": 123} # Intentionally contains an invalid city value
    processed_user_data = process_dictionary_input(user_data, key_type=str, value_type=str, required_keys=["name", "age"])
    if processed_user_data:
        print(f"Processed user data: {processed_user_data}")
    else:
        print("User data dictionary is invalid.")

    # Example of allowing None
    optional_value = None
    is_valid_none = validate_input(optional_value, str, allow_none=True)
    print(f"Is None valid for string (allowing None)? {is_valid_none}")

    is_valid_none = validate_input(optional_value, str, allow_none=False)
    print(f"Is None valid for string (not allowing None)? {is_valid_none}")