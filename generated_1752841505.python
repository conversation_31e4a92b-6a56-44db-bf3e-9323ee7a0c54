import logging

# Configure logging for better debugging and monitoring
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def validate_input(input_data, expected_type, allow_empty=False):
    """
    Validates the input data against the expected type and optional constraints.

    Args:
        input_data: The data to be validated.
        expected_type: The expected data type (e.g., str, int, float, list, dict).
        allow_empty:  Boolean indicating whether empty strings or lists are allowed. Defaults to False.

    Returns:
        The validated input data if it's valid.

    Raises:
        TypeError: If the input data is not of the expected type.
        ValueError: If the input data is empty when it's not allowed, or if it fails other validation checks.
    """

    try:
        if not isinstance(input_data, expected_type):
            raise TypeError(f"Input data must be of type {expected_type.__name__}, but got {type(input_data).__name__}")

        if not allow_empty:
            if isinstance(input_data, str) and not input_data.strip():  # Check for empty strings after removing whitespace
                raise ValueError("Input string cannot be empty.")
            elif isinstance(input_data, (list, dict, set)) and not input_data:
                raise ValueError("Input list/dict/set cannot be empty.")

        return input_data  # Return the validated input data

    except (TypeError, ValueError) as e:
        logging.error(f"Input validation error: {e}")
        raise  # Re-raise the exception to signal the validation failure to the caller


def process_string_input(input_string):
    """
    Processes a string input by converting it to lowercase and removing leading/trailing whitespace.

    Args:
        input_string: The string to process.

    Returns:
        The processed string.

    Raises:
        TypeError: If the input is not a string.
        ValueError: If the input string is empty after stripping whitespace.
    """
    try:
        validated_string = validate_input(input_string, str)
        processed_string = validated_string.strip().lower()
        if not processed_string:
            raise ValueError("Input string is empty after processing.")
        return processed_string
    except (TypeError, ValueError) as e:
        logging.error(f"Error processing string input: {e}")
        raise


def process_numeric_input(input_number, min_value=None, max_value=None):
    """
    Processes a numeric input (int or float) by validating its type and optional range.

    Args:
        input_number: The number to process.
        min_value: Optional minimum allowed value.
        max_value: Optional maximum allowed value.

    Returns:
        The validated number.

    Raises:
        TypeError: If the input is not an int or float.
        ValueError: If the input is outside the specified range.
    """
    try:
        validated_number = validate_input(input_number, (int, float))

        if min_value is not None and validated_number < min_value:
            raise ValueError(f"Input number must be greater than or equal to {min_value}.")

        if max_value is not None and validated_number > max_value:
            raise ValueError(f"Input number must be less than or equal to {max_value}.")

        return validated_number
    except (TypeError, ValueError) as e:
        logging.error(f"Error processing numeric input: {e}")
        raise


def process_list_input(input_list, element_type=None, allow_empty=False):
    """
    Processes a list input by validating its type and optionally validating the type of its elements.

    Args:
        input_list: The list to process.
        element_type: Optional expected type for elements in the list.  If None, no element type checking is performed.
        allow_empty: Boolean indicating whether an empty list is allowed. Defaults to False.

    Returns:
        The validated list.

    Raises:
        TypeError: If the input is not a list or if an element has the wrong type.
        ValueError: If the list is empty when it's not allowed.
    """
    try:
        validated_list = validate_input(input_list, list, allow_empty=allow_empty)

        if element_type:
            for i, element in enumerate(validated_list):
                try:
                    validate_input(element, element_type)  # Validate each element's type
                except (TypeError, ValueError) as e:
                    raise ValueError(f"Element at index {i} is invalid: {e}") from e

        return validated_list
    except (TypeError, ValueError) as e:
        logging.error(f"Error processing list input: {e}")
        raise


def process_dictionary_input(input_dict, key_type=None, value_type=None, allow_empty=False):
    """
    Processes a dictionary input by validating its type and optionally validating the type of its keys and values.

    Args:
        input_dict: The dictionary to process.
        key_type: Optional expected type for keys in the dictionary. If None, no key type checking is performed.
        value_type: Optional expected type for values in the dictionary. If None, no value type checking is performed.
        allow_empty: Boolean indicating whether an empty dictionary is allowed. Defaults to False.

    Returns:
        The validated dictionary.

    Raises:
        TypeError: If the input is not a dictionary or if a key/value has the wrong type.
        ValueError: If the dictionary is empty when it's not allowed.
    """
    try:
        validated_dict = validate_input(input_dict, dict, allow_empty=allow_empty)

        if key_type or value_type:
            for key, value in validated_dict.items():
                if key_type:
                    try:
                        validate_input(key, key_type)
                    except (TypeError, ValueError) as e:
                        raise ValueError(f"Key '{key}' is invalid: {e}") from e
                if value_type:
                    try:
                        validate_input(value, value_type)
                    except (TypeError, ValueError) as e:
                        raise ValueError(f"Value for key '{key}' is invalid: {e}") from e

        return validated_dict
    except (TypeError, ValueError) as e:
        logging.error(f"Error processing dictionary input: {e}")
        raise


# Example Usage (demonstrates how to use the functions)
if __name__ == '__main__':
    try:
        # String input
        name = process_string_input("  John Doe  ")
        print(f"Processed name: {name}")

        # Numeric input
        age = process_numeric_input(30, min_value=0, max_value=120)
        print(f"Processed age: {age}")

        # List input
        numbers = process_list_input([1, 2, 3, 4, 5], element_type=int)
        print(f"Processed numbers: {numbers}")

        # Dictionary input
        person = process_dictionary_input({"name": "Alice", "age": 25}, key_type=str, value_type=(str, int))
        print(f"Processed person: {person}")

        # Example of error handling
        invalid_age = process_numeric_input(-5, min_value=0)  # This will raise a ValueError
        print(f"Invalid age: {invalid_age}")  # This line will not be executed

    except ValueError as e:
        print(f"Error: {e}")
    except TypeError as e:
        print(f"Type Error: {e}")