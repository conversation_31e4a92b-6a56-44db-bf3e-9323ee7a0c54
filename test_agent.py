#!/usr/bin/env python3
"""
Test script for the upgraded OpenCode AI Terminal Agent
"""

import os
import sys
from agent import AdvancedCodingAgent, Config<PERSON><PERSON><PERSON>, AIProviderManager

def test_config_manager():
    """Test configuration management"""
    print("🧪 Testing Configuration Manager...")
    
    config = ConfigManager()
    
    # Test setting API key
    result = config.set_api_key("gemini", "test-key-123")
    print(f"✅ Set API key: {result}")
    
    # Test getting API key
    key = config.get_api_key("gemini")
    print(f"✅ Retrieved API key: {key[:8]}..." if key else "❌ No key found")
    
    # Test enabled providers
    enabled = config.get_enabled_providers()
    print(f"✅ Enabled providers: {enabled}")
    
    print("✅ Configuration Manager tests passed!\n")

def test_ai_provider_manager():
    """Test AI provider management"""
    print("🧪 Testing AI Provider Manager...")
    
    config = ConfigManager()
    ai_manager = AIProviderManager(config)
    
    # Test available providers
    providers = ai_manager.get_available_providers()
    print(f"✅ Available providers: {list(providers.keys())}")
    
    # Test current LLM
    llm = ai_manager.get_current_llm()
    print(f"✅ Current LLM: {type(llm).__name__}")
    
    print("✅ AI Provider Manager tests passed!\n")

def test_agent_initialization():
    """Test agent initialization"""
    print("🧪 Testing Agent Initialization...")
    
    try:
        agent = AdvancedCodingAgent()
        print("✅ Agent initialized successfully")
        
        # Test TUI initialization
        print(f"✅ TUI initialized: {agent.tui is not None}")
        
        # Test database manager
        print(f"✅ Database manager: {agent.db_manager is not None}")
        
        # Test terminal capabilities
        print(f"✅ Terminal capabilities: {agent.terminal_capabilities is not None}")
        
        print("✅ Agent initialization tests passed!\n")
        
    except Exception as e:
        print(f"❌ Agent initialization failed: {e}")
        return False
        
    return True

def test_terminal_capabilities():
    """Test advanced terminal capabilities"""
    print("🧪 Testing Terminal Capabilities...")
    
    try:
        agent = AdvancedCodingAgent()
        
        # Test project analysis
        analysis = agent.terminal_capabilities.analyze_project()
        print("✅ Project analysis completed")
        
        # Test workflow creation
        workflow = agent.terminal_capabilities.create_workflow("setup project")
        print("✅ Workflow creation completed")
        
        # Test intelligent suggestions
        suggestions = agent.terminal_capabilities.intelligent_suggestions("error in code")
        print(f"✅ Generated {len(suggestions)} suggestions")
        
        print("✅ Terminal capabilities tests passed!\n")
        
    except Exception as e:
        print(f"❌ Terminal capabilities test failed: {e}")
        return False
        
    return True

def main():
    """Run all tests"""
    print("🚀 Starting OpenCode AI Terminal Agent Tests")
    print("=" * 60)
    
    tests = [
        test_config_manager,
        test_ai_provider_manager,
        test_agent_initialization,
        test_terminal_capabilities
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed: {e}\n")
    
    print("=" * 60)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Agent is ready to use.")
        print("\n💡 To start the agent, run: python agent.py")
        print("🔧 Use Ctrl+G to configure API keys")
        print("📚 Use Ctrl+? for help and shortcuts")
    else:
        print("⚠️ Some tests failed. Please check the configuration.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
