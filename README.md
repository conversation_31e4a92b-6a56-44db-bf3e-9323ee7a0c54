# 🚀 OpenCode AI Terminal - Advanced Coding Agent

A sophisticated terminal-based AI coding assistant inspired by OpenCode AI, featuring multi-provider AI support, advanced code analysis, and intelligent workflow automation.

## ✨ Features

### 🎯 Core Capabilities
- **Multi-Provider AI Support**: Gemini, OpenAI, Anthropic, Mistral, DeepSeek, Groq, Together AI
- **OpenCode-style TUI**: Rich terminal interface with panels, sessions, and navigation
- **Session Management**: Persistent conversations with SQLite storage
- **Advanced Code Analysis**: Security auditing, performance profiling, refactoring suggestions
- **Cross-language Conversion**: Convert code between Python, JavaScript, Java, and more
- **Intelligent Workflows**: Automated task pipelines for complex development workflows

### 🔧 Advanced Terminal Capabilities
- **Project Analysis**: Comprehensive project structure analysis and insights
- **Workflow Engine**: Automated workflows for setup, deployment, testing, and more
- **Code Intelligence**: Context-aware suggestions and predictive recommendations
- **Multi-step Pipelines**: Complex automation chains for development tasks
- **Git Integration**: Advanced Git operations and repository management
- **Package Management**: Smart dependency management across languages

### 🎨 User Interface
- **Rich TUI**: Modern terminal interface with proper panels and styling
- **Keyboard Shortcuts**: OpenCode-style navigation and commands
- **Session Switching**: Easy switching between multiple conversations
- **Configuration Menu**: Built-in API key management (Ctrl+G)
- **Real-time Updates**: Live interface updates and status indicators

## 🚀 Quick Start

### Prerequisites
```bash
pip install rich langchain langchain-google-genai langchain-openai langchain-anthropic
pip install langchain-mistralai langchain-groq requests pyyaml
```

### Installation
1. Clone or download the agent files
2. Set up at least one AI provider API key:
   ```bash
   export GEMINI_API_KEY="your-gemini-key"
   export OPENAI_API_KEY="your-openai-key"
   export ANTHROPIC_API_KEY="your-claude-key"
   # ... other providers
   ```

### Running the Agent
```bash
python agent.py
```

## ⌨️ Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl+C` | Quit application |
| `Ctrl+G` | Configuration menu |
| `Ctrl+?` | Help dialog |
| `Ctrl+L` | View logs |
| `Ctrl+A` | Switch session |
| `Ctrl+O` | Change AI model |
| `Ctrl+K` | Command dialog |
| `Ctrl+N` | New session |
| `Ctrl+S` | Send message |
| `Esc` | Close dialog/return to chat |

## 🤖 Supported AI Providers

### Configured Providers
- **Google Gemini**: `GEMINI_API_KEY`
- **OpenAI**: `OPENAI_API_KEY` 
- **Anthropic Claude**: `ANTHROPIC_API_KEY`
- **Mistral AI**: `MISTRAL_API_KEY`
- **DeepSeek**: `DEEPSEEK_API_KEY`
- **Groq**: `GROQ_API_KEY`
- **Together AI**: `TOGETHER_API_KEY`

### Available Models
- Gemini: 2.0-flash, 2.5-pro, 1.5-pro, 1.5-flash
- OpenAI: GPT-4o, GPT-4o-mini, GPT-4, GPT-3.5-turbo, o1-preview, o1-mini
- Claude: 3.5-sonnet, 3.5-haiku, 3-opus
- Mistral: large-latest, medium-latest, small-latest, codestral-latest
- DeepSeek: deepseek-chat, deepseek-coder
- Groq: llama-3.1-70b-versatile, llama-3.1-8b-instant, mixtral-8x7b-32768
- Together: Various Llama and Mixtral models

## 🛠️ Advanced Tools

### Code Analysis
- **Security Audit**: Detect SQL injection, XSS, hardcoded secrets
- **Performance Profile**: Identify bottlenecks and optimization opportunities
- **Refactoring Engine**: Automated code improvements and suggestions
- **Cross-language Conversion**: Convert between programming languages

### Development Workflows
- **Project Analysis**: Comprehensive project structure insights
- **Automated Pipelines**: Multi-step development workflows
- **Git Operations**: Advanced repository management
- **Package Management**: Smart dependency handling
- **Test Automation**: Automated testing and coverage analysis

### Intelligence Features
- **Predictive Caching**: Background prediction of next actions
- **Pattern Analysis**: Learning from usage patterns
- **Context Engine**: Intelligent context management
- **Smart Suggestions**: Context-aware recommendations

## 📁 Project Structure

```
project/
├── agent.py              # Main agent implementation
├── test_agent.py         # Test suite
├── README.md            # This file
└── .opencode/           # Configuration directory
    ├── config.json      # Provider configuration
    └── sessions.db      # Session storage
```

## 🔧 Configuration

### Using Environment Variables
Set API keys as environment variables:
```bash
export GEMINI_API_KEY="your-key-here"
export OPENAI_API_KEY="your-key-here"
# ... etc
```

### Using Configuration Menu
1. Start the agent: `python agent.py`
2. Press `Ctrl+G` to open configuration
3. Follow the prompts to set up API keys

## 🧪 Testing

Run the test suite to verify functionality:
```bash
python test_agent.py
```

## 🎯 Usage Examples

### Basic Chat
```
🤖 > Create a Python web scraper
🤖 > Analyze this code for security issues
🤖 > Convert this Python code to JavaScript
```

### Advanced Commands
```
🤖 > analyze_project
🤖 > create_workflow "deploy application"
🤖 > security_audit [paste code here]
🤖 > performance_profile [paste code here]
```

## 🚀 Advanced Features

### Session Management
- Persistent conversation history
- Multiple concurrent sessions
- Session switching with Ctrl+A
- Auto-save functionality

### Multi-Provider Support
- Seamless switching between AI providers
- Model-specific optimizations
- Fallback mechanisms
- Provider health monitoring

### Intelligent Workflows
- Automated project setup
- Deployment pipelines
- Code review workflows
- Testing automation

## 🔍 Troubleshooting

### Common Issues
1. **No AI providers configured**: Use Ctrl+G to set up API keys
2. **Import errors**: Install required dependencies with pip
3. **Layout errors**: Ensure Rich library is properly installed
4. **Memory warnings**: These are deprecation warnings and can be ignored

### Getting Help
- Press `Ctrl+?` for in-app help
- Check logs with `Ctrl+L`
- Run test suite: `python test_agent.py`

## 🎉 What's New

### OpenCode AI Features
- ✅ Multi-provider AI support with 7+ providers
- ✅ Rich terminal UI with proper panels and navigation
- ✅ Session management with persistent storage
- ✅ Advanced code analysis and security auditing
- ✅ Intelligent workflows and automation
- ✅ Configuration management system
- ✅ Keyboard shortcuts and navigation
- ✅ Cross-language code conversion
- ✅ Project analysis and insights
- ✅ Performance profiling and optimization

### Removed
- ❌ Dummy provider (replaced with proper configuration flow)
- ❌ Basic CLI interface (upgraded to Rich TUI)
- ❌ Single provider limitation (now supports 7+ providers)

---

**🎯 Ready to code with AI assistance!** Start with `python agent.py` and press `Ctrl+?` for help.
